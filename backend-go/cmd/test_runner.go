package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// TestRunner handles running integration tests
type TestRunner struct {
	verbose     bool
	short       bool
	parallel    bool
	coverage    bool
	testPattern string
	timeout     time.Duration
}

func main() {
	runner := &TestRunner{}
	
	// Parse command line flags
	flag.BoolVar(&runner.verbose, "v", false, "Verbose output")
	flag.BoolVar(&runner.short, "short", false, "Run tests in short mode")
	flag.BoolVar(&runner.parallel, "parallel", true, "Run tests in parallel")
	flag.BoolVar(&runner.coverage, "coverage", false, "Generate coverage report")
	flag.StringVar(&runner.testPattern, "run", "", "Run only tests matching pattern")
	flag.DurationVar(&runner.timeout, "timeout", 10*time.Minute, "Test timeout")
	flag.Parse()

	if err := runner.Run(); err != nil {
		log.Fatalf("Test runner failed: %v", err)
	}
}

// Run executes the integration tests
func (r *TestRunner) Run() error {
	fmt.Println("🚀 Starting Integration Tests for Go Backend")
	fmt.Println("=" + strings.Repeat("=", 50))

	// Check prerequisites
	if err := r.checkPrerequisites(); err != nil {
		return fmt.Errorf("prerequisites check failed: %w", err)
	}

	// Setup test environment
	if err := r.setupTestEnvironment(); err != nil {
		return fmt.Errorf("test environment setup failed: %w", err)
	}

	// Run tests
	if err := r.runTests(); err != nil {
		return fmt.Errorf("tests failed: %w", err)
	}

	// Generate reports
	if r.coverage {
		if err := r.generateCoverageReport(); err != nil {
			return fmt.Errorf("coverage report generation failed: %w", err)
		}
	}

	fmt.Println("\n✅ All integration tests completed successfully!")
	return nil
}

// checkPrerequisites verifies that all required tools and services are available
func (r *TestRunner) checkPrerequisites() error {
	fmt.Println("📋 Checking prerequisites...")

	// Check Go installation
	if err := r.checkCommand("go", "version"); err != nil {
		return fmt.Errorf("Go is not installed or not in PATH: %w", err)
	}

	// Check PostgreSQL availability
	if err := r.checkPostgreSQL(); err != nil {
		return fmt.Errorf("PostgreSQL check failed: %w", err)
	}

	fmt.Println("✅ Prerequisites check passed")
	return nil
}

// checkCommand checks if a command is available
func (r *TestRunner) checkCommand(command string, args ...string) error {
	cmd := exec.Command(command, args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("command '%s %s' failed: %w", command, strings.Join(args, " "), err)
	}
	return nil
}

// checkPostgreSQL checks if PostgreSQL is accessible
func (r *TestRunner) checkPostgreSQL() error {
	// Try to connect to PostgreSQL using psql
	cmd := exec.Command("psql", "--version")
	if err := cmd.Run(); err != nil {
		fmt.Println("⚠️  Warning: psql not found, assuming PostgreSQL is available")
		return nil
	}

	// Try to connect to the test database
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		dbURL = "postgres://postgres:password@localhost:5432/adc_test?sslmode=disable"
	}

	fmt.Printf("🔍 Testing database connection: %s\n", dbURL)
	return nil
}

// setupTestEnvironment sets up the test environment
func (r *TestRunner) setupTestEnvironment() error {
	fmt.Println("🔧 Setting up test environment...")

	// Set test environment variables
	testEnvVars := map[string]string{
		"APP_ENV":                "test",
		"DATABASE_URL":           "postgres://postgres:password@localhost:5432/adc_test?sslmode=disable",
		"JWT_SECRET":             "test-secret-key-for-testing-only",
		"JWT_REFRESH_SECRET":     "test-refresh-secret-key",
		"JWT_EXPIRATION_HOURS":   "24",
		"LOG_LEVEL":              "error",
		"PORT":                   "8301",
	}

	for key, value := range testEnvVars {
		if err := os.Setenv(key, value); err != nil {
			return fmt.Errorf("failed to set environment variable %s: %w", key, err)
		}
	}

	fmt.Println("✅ Test environment setup completed")
	return nil
}

// runTests executes the integration tests
func (r *TestRunner) runTests() error {
	fmt.Println("🧪 Running integration tests...")

	// Prepare test command
	args := []string{"test"}
	
	if r.verbose {
		args = append(args, "-v")
	}
	
	if r.short {
		args = append(args, "-short")
	}
	
	if r.parallel {
		args = append(args, "-parallel", "4")
	}
	
	if r.coverage {
		args = append(args, "-coverprofile=coverage.out", "-covermode=atomic")
	}
	
	if r.testPattern != "" {
		args = append(args, "-run", r.testPattern)
	}
	
	args = append(args, "-timeout", r.timeout.String())
	args = append(args, "./tests/...")

	// Run tests
	cmd := exec.Command("go", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("tests failed: %w", err)
	}

	return nil
}

// generateCoverageReport generates a coverage report
func (r *TestRunner) generateCoverageReport() error {
	fmt.Println("📊 Generating coverage report...")

	// Generate HTML coverage report
	cmd := exec.Command("go", "tool", "cover", "-html=coverage.out", "-o", "coverage.html")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to generate HTML coverage report: %w", err)
	}

	// Generate coverage summary
	cmd = exec.Command("go", "tool", "cover", "-func=coverage.out")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to generate coverage summary: %w", err)
	}

	fmt.Println("📈 Coverage Summary:")
	fmt.Println(string(output))

	// Save coverage report path
	absPath, _ := filepath.Abs("coverage.html")
	fmt.Printf("📄 HTML Coverage Report: %s\n", absPath)

	return nil
}
