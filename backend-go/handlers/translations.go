package handlers

import (
	"fmt"
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TranslationHandler handles translation-related routes
type TranslationHandler struct{}

// NewTranslationHandler creates a new translation handler
func NewTranslationHandler() *TranslationHandler {
	return &TranslationHandler{}
}

// Request structures
type CreateTranslationKeyRequest struct {
	ResourceID    *string `json:"resource_id"`
	ProjectID     *string `json:"project_id"`
	KeyName       string  `json:"key_name" binding:"required,min=1,max=255"`
	Description   *string `json:"description"`
	Context       *string `json:"context"`
	IsPlural      *bool   `json:"is_plural"`
	MaxLength     *int    `json:"max_length"`
	ScreenshotURL *string `json:"screenshot_url"`
}

type CreateTranslationRequest struct {
	KeyID      string `json:"key_id" binding:"required"`
	LocaleID   string `json:"locale_id" binding:"required"`
	Content    string `json:"content" binding:"required"`
	IsFuzzy    *bool  `json:"is_fuzzy"`
	IsReviewed *bool  `json:"is_reviewed"`
}

type UpdateTranslationKeyRequest struct {
	KeyName       *string `json:"key_name"`
	Description   *string `json:"description"`
	Context       *string `json:"context"`
	IsPlural      *bool   `json:"is_plural"`
	MaxLength     *int    `json:"max_length"`
	ScreenshotURL *string `json:"screenshot_url"`
}

type UpdateTranslationRequest struct {
	Content    string `json:"content" binding:"required"`
	IsFuzzy    *bool  `json:"is_fuzzy"`
	IsReviewed *bool  `json:"is_reviewed"`
}

// Response structures
type TranslationKeyResponse struct {
	ID            string    `json:"id"`
	ResourceID    *string   `json:"resource_id"`
	ProjectID     *string   `json:"project_id"`
	KeyName       string    `json:"key_name"`
	Description   *string   `json:"description"`
	Context       *string   `json:"context"`
	IsPlural      *bool     `json:"is_plural"`
	MaxLength     *int      `json:"max_length"`
	ScreenshotURL *string   `json:"screenshot_url"`
	CreatedBy     *string   `json:"created_by"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type TranslationResponse struct {
	ID         string     `json:"id"`
	KeyID      string     `json:"key_id"`
	LocaleID   string     `json:"locale_id"`
	Content    string     `json:"content"`
	IsFuzzy    *bool      `json:"is_fuzzy"`
	IsReviewed *bool      `json:"is_reviewed"`
	ReviewedBy *string    `json:"reviewed_by"`
	ReviewedAt *time.Time `json:"reviewed_at"`
	CreatedBy  *string    `json:"created_by"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
}

type TranslationHistoryResponse struct {
	ID            string    `json:"id"`
	TranslationID string    `json:"translation_id"`
	Content       string    `json:"content"`
	Action        string    `json:"action"`
	PerformedBy   *string   `json:"performed_by"`
	CreatedAt     time.Time `json:"created_at"`
}

// ListTranslationKeys handles GET /api/translations/keys
func (h *TranslationHandler) ListTranslationKeys(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	// Get query parameters
	resourceIDStr := c.Query("resource_id")
	projectIDStr := c.Query("project_id")

	// Either resource_id or project_id must be provided
	if resourceIDStr == "" && projectIDStr == "" {
		utils.BadRequest(c, "Either resource_id or project_id must be provided")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get pagination parameters
	pagination := utils.GetPaginationParams(c)

	var keys []models.TranslationKey
	var total int64
	var query *gorm.DB

	if resourceIDStr != "" {
		// Query by resource_id
		resourceID, err := uuid.Parse(resourceIDStr)
		if err != nil {
			utils.BadRequest(c, "Invalid resource_id format")
			return
		}

		// Check if user has access to the resource (through project -> organization)
		var resource models.Resource
		if err := db.Preload("Project.Organization").Where("id = ?", resourceID).First(&resource).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.NotFound(c, "Resource not found")
				return
			}
			utils.LogError(c, "Failed to get resource", err, nil)
			utils.InternalServerError(c, "Failed to retrieve resource")
			return
		}

		// Check organization membership
		if !h.checkOrganizationAccess(db, resource.Project.OrganizationID, user.ID) {
			utils.Forbidden(c, "You don't have access to this resource")
			return
		}

		query = db.Where("resource_id = ?", resourceID)
	} else {
		// Query by project_id
		projectID, err := uuid.Parse(projectIDStr)
		if err != nil {
			utils.BadRequest(c, "Invalid project_id format")
			return
		}

		// Check if user has access to the project (through organization)
		var project models.Project
		if err := db.Where("id = ?", projectID).First(&project).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.NotFound(c, "Project not found")
				return
			}
			utils.LogError(c, "Failed to get project", err, nil)
			utils.InternalServerError(c, "Failed to retrieve project")
			return
		}

		// Check organization membership
		if !h.checkOrganizationAccess(db, project.OrganizationID, user.ID) {
			utils.Forbidden(c, "You don't have access to this project")
			return
		}

		query = db.Where("project_id = ?", projectID)
	}

	// Count total keys
	if err := query.Model(&models.TranslationKey{}).Count(&total).Error; err != nil {
		utils.LogError(c, "Failed to count translation keys", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation keys")
		return
	}

	// Get keys with pagination
	if err := query.Preload("Resource").Preload("Project").Preload("Creator").
		Order("key_name ASC").
		Limit(int(pagination.PerPage)).
		Offset(int(pagination.Offset)).
		Find(&keys).Error; err != nil {
		utils.LogError(c, "Failed to list translation keys", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation keys")
		return
	}

	// Convert to response format
	response := make([]TranslationKeyResponse, len(keys))
	for i, key := range keys {
		response[i] = TranslationKeyResponse{
			ID:            key.ID.String(),
			ResourceID:    uuidPtrToStringPtr(key.ResourceID),
			ProjectID:     uuidPtrToStringPtr(key.ProjectID),
			KeyName:       key.KeyName,
			Description:   key.Description,
			Context:       key.Context,
			IsPlural:      key.IsPlural,
			MaxLength:     key.MaxLength,
			ScreenshotURL: key.ScreenshotURL,
			CreatedBy:     uuidPtrToStringPtr(key.CreatedBy),
			CreatedAt:     key.CreatedAt,
			UpdatedAt:     key.UpdatedAt,
		}
	}

	utils.PaginatedResponse(c, response, pagination, uint64(total))
}

// CreateTranslationKey handles POST /api/translations/keys
func (h *TranslationHandler) CreateTranslationKey(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	var req CreateTranslationKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Either resource_id or project_id must be provided
	if req.ResourceID == nil && req.ProjectID == nil {
		utils.BadRequest(c, "Either resource_id or project_id must be provided")
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(user.ID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	var organizationID uuid.UUID

	// Validate access and get organization ID
	if req.ResourceID != nil {
		resourceID, err := uuid.Parse(*req.ResourceID)
		if err != nil {
			utils.BadRequest(c, "Invalid resource_id format")
			return
		}

		// Check if user has access to the resource
		var resource models.Resource
		if err := db.Preload("Project").Where("id = ?", resourceID).First(&resource).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.NotFound(c, "Resource not found")
				return
			}
			utils.LogError(c, "Failed to get resource", err, nil)
			utils.InternalServerError(c, "Failed to retrieve resource")
			return
		}

		organizationID = resource.Project.OrganizationID
	} else {
		projectID, err := uuid.Parse(*req.ProjectID)
		if err != nil {
			utils.BadRequest(c, "Invalid project_id format")
			return
		}

		// Check if user has access to the project
		var project models.Project
		if err := db.Where("id = ?", projectID).First(&project).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.NotFound(c, "Project not found")
				return
			}
			utils.LogError(c, "Failed to get project", err, nil)
			utils.InternalServerError(c, "Failed to retrieve project")
			return
		}

		organizationID = project.OrganizationID
	}

	// Check organization membership
	if !h.checkOrganizationAccess(db, organizationID, user.ID) {
		utils.Forbidden(c, "You don't have access to this organization")
		return
	}

	// Check if key with same name already exists
	var existingKey models.TranslationKey
	var checkQuery *gorm.DB

	if req.ResourceID != nil {
		resourceID, _ := uuid.Parse(*req.ResourceID)
		checkQuery = db.Where("resource_id = ? AND key_name = ?", resourceID, req.KeyName)
	} else {
		projectID, _ := uuid.Parse(*req.ProjectID)
		checkQuery = db.Where("project_id = ? AND key_name = ?", projectID, req.KeyName)
	}

	if err := checkQuery.First(&existingKey).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("Translation key with name '%s' already exists", req.KeyName))
		return
	}

	// Create translation key
	key := models.TranslationKey{
		KeyName:       req.KeyName,
		Description:   req.Description,
		Context:       req.Context,
		IsPlural:      req.IsPlural,
		MaxLength:     req.MaxLength,
		ScreenshotURL: req.ScreenshotURL,
		CreatedBy:     &userID,
	}

	if req.ResourceID != nil {
		resourceID, _ := uuid.Parse(*req.ResourceID)
		key.ResourceID = &resourceID
	}
	if req.ProjectID != nil {
		projectID, _ := uuid.Parse(*req.ProjectID)
		key.ProjectID = &projectID
	}

	if err := db.Create(&key).Error; err != nil {
		utils.LogError(c, "Failed to create translation key", err, nil)
		utils.InternalServerError(c, "Failed to create translation key")
		return
	}

	// Reload key with relations
	if err := db.Preload("Resource").Preload("Project").Preload("Creator").
		Where("id = ?", key.ID).First(&key).Error; err != nil {
		utils.LogError(c, "Failed to reload created translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve created translation key")
		return
	}

	// Convert to response format
	response := TranslationKeyResponse{
		ID:            key.ID.String(),
		ResourceID:    uuidPtrToStringPtr(key.ResourceID),
		ProjectID:     uuidPtrToStringPtr(key.ProjectID),
		KeyName:       key.KeyName,
		Description:   key.Description,
		Context:       key.Context,
		IsPlural:      key.IsPlural,
		MaxLength:     key.MaxLength,
		ScreenshotURL: key.ScreenshotURL,
		CreatedBy:     uuidPtrToStringPtr(key.CreatedBy),
		CreatedAt:     key.CreatedAt,
		UpdatedAt:     key.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Translation key created successfully")
}

// GetTranslationKey handles GET /api/translations/keys/:id
func (h *TranslationHandler) GetTranslationKey(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := uuid.Parse(keyIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation key ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get translation key
	var key models.TranslationKey
	if err := db.Preload("Resource.Project").Preload("Project").Preload("Creator").
		Where("id = ?", keyID).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation key not found")
			return
		}
		utils.LogError(c, "Failed to get translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation key")
		return
	}

	// Check organization access
	var organizationID uuid.UUID
	if key.ResourceID != nil && key.Resource != nil {
		organizationID = key.Resource.Project.OrganizationID
	} else if key.ProjectID != nil && key.Project != nil {
		organizationID = key.Project.OrganizationID
	} else {
		utils.InternalServerError(c, "Translation key has no valid resource or project")
		return
	}

	if !h.checkOrganizationAccess(db, organizationID, user.ID) {
		utils.Forbidden(c, "You don't have access to this translation key")
		return
	}

	// Convert to response format
	response := TranslationKeyResponse{
		ID:            key.ID.String(),
		ResourceID:    uuidPtrToStringPtr(key.ResourceID),
		ProjectID:     uuidPtrToStringPtr(key.ProjectID),
		KeyName:       key.KeyName,
		Description:   key.Description,
		Context:       key.Context,
		IsPlural:      key.IsPlural,
		MaxLength:     key.MaxLength,
		ScreenshotURL: key.ScreenshotURL,
		CreatedBy:     uuidPtrToStringPtr(key.CreatedBy),
		CreatedAt:     key.CreatedAt,
		UpdatedAt:     key.UpdatedAt,
	}

	utils.Success(c, response)
}

// UpdateTranslationKey handles PUT /api/translations/keys/:id
func (h *TranslationHandler) UpdateTranslationKey(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := uuid.Parse(keyIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation key ID format")
		return
	}

	var req UpdateTranslationKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get existing translation key with relations
	var key models.TranslationKey
	if err := db.Preload("Resource.Project").Preload("Project").
		Where("id = ?", keyID).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation key not found")
			return
		}
		utils.LogError(c, "Failed to get translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation key")
		return
	}

	// Check organization access
	var organizationID uuid.UUID
	if key.ResourceID != nil && key.Resource != nil {
		organizationID = key.Resource.Project.OrganizationID
	} else if key.ProjectID != nil && key.Project != nil {
		organizationID = key.Project.OrganizationID
	} else {
		utils.InternalServerError(c, "Translation key has no valid resource or project")
		return
	}

	if !h.checkOrganizationAccess(db, organizationID, user.ID) {
		utils.Forbidden(c, "You don't have access to this translation key")
		return
	}

	// Check if key name is being updated and if it conflicts
	if req.KeyName != nil && *req.KeyName != key.KeyName {
		var existingKey models.TranslationKey
		var checkQuery *gorm.DB

		if key.ResourceID != nil {
			checkQuery = db.Where("resource_id = ? AND key_name = ? AND id != ?", key.ResourceID, *req.KeyName, keyID)
		} else {
			checkQuery = db.Where("project_id = ? AND key_name = ? AND id != ?", key.ProjectID, *req.KeyName, keyID)
		}

		if err := checkQuery.First(&existingKey).Error; err == nil {
			utils.Conflict(c, fmt.Sprintf("Translation key with name '%s' already exists", *req.KeyName))
			return
		}
	}

	// Update fields
	updates := map[string]interface{}{
		"updated_at": utils.GetCurrentTime(),
	}

	if req.KeyName != nil {
		updates["key_name"] = *req.KeyName
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Context != nil {
		updates["context"] = *req.Context
	}
	if req.IsPlural != nil {
		updates["is_plural"] = *req.IsPlural
	}
	if req.MaxLength != nil {
		updates["max_length"] = *req.MaxLength
	}
	if req.ScreenshotURL != nil {
		updates["screenshot_url"] = *req.ScreenshotURL
	}

	// Update the translation key
	if err := db.Model(&key).Updates(updates).Error; err != nil {
		utils.LogError(c, "Failed to update translation key", err, nil)
		utils.InternalServerError(c, "Failed to update translation key")
		return
	}

	// Reload the updated key
	if err := db.Preload("Resource.Project").Preload("Project").
		Where("id = ?", keyID).First(&key).Error; err != nil {
		utils.LogError(c, "Failed to reload updated translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve updated translation key")
		return
	}

	// Convert to response format
	response := TranslationKeyResponse{
		ID:            key.ID.String(),
		ResourceID:    uuidPtrToStringPtr(key.ResourceID),
		ProjectID:     uuidPtrToStringPtr(key.ProjectID),
		KeyName:       key.KeyName,
		Description:   key.Description,
		Context:       key.Context,
		IsPlural:      key.IsPlural,
		MaxLength:     key.MaxLength,
		ScreenshotURL: key.ScreenshotURL,
		CreatedBy:     uuidPtrToStringPtr(key.CreatedBy),
		CreatedAt:     key.CreatedAt,
		UpdatedAt:     key.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Translation key updated successfully")
}

// CreateTranslation handles POST /api/translations
func (h *TranslationHandler) CreateTranslation(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	var req CreateTranslationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse IDs
	keyID, err := uuid.Parse(req.KeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid key_id format")
		return
	}

	localeID, err := uuid.Parse(req.LocaleID)
	if err != nil {
		utils.BadRequest(c, "Invalid locale_id format")
		return
	}

	userID, err := uuid.Parse(user.ID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if translation key exists and user has access
	var key models.TranslationKey
	if err := db.Preload("Resource.Project").Preload("Project").
		Where("id = ?", keyID).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation key not found")
			return
		}
		utils.LogError(c, "Failed to get translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation key")
		return
	}

	// Check organization access
	var organizationID uuid.UUID
	if key.ResourceID != nil && key.Resource != nil {
		organizationID = key.Resource.Project.OrganizationID
	} else if key.ProjectID != nil && key.Project != nil {
		organizationID = key.Project.OrganizationID
	} else {
		utils.InternalServerError(c, "Translation key has no valid resource or project")
		return
	}

	if !h.checkOrganizationAccess(db, organizationID, user.ID) {
		utils.Forbidden(c, "You don't have access to this translation key")
		return
	}

	// Check if locale exists
	var locale models.Locale
	if err := db.Where("id = ?", localeID).First(&locale).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Locale not found")
			return
		}
		utils.LogError(c, "Failed to get locale", err, nil)
		utils.InternalServerError(c, "Failed to retrieve locale")
		return
	}

	// Check if translation already exists
	var existingTranslation models.Translation
	if err := db.Where("key_id = ? AND locale_id = ?", keyID, localeID).
		First(&existingTranslation).Error; err == nil {
		utils.Conflict(c, "Translation already exists for this key and locale")
		return
	}

	// Create translation
	translation := models.Translation{
		KeyID:      keyID,
		LocaleID:   localeID,
		Content:    req.Content,
		IsFuzzy:    req.IsFuzzy,
		IsReviewed: req.IsReviewed,
		CreatedBy:  &userID,
	}

	// Start transaction for translation creation and history
	tx := db.Begin()

	if err := tx.Create(&translation).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to create translation", err, nil)
		utils.InternalServerError(c, "Failed to create translation")
		return
	}

	// Create history entry
	history := models.TranslationHistory{
		TranslationID: translation.ID,
		Content:       translation.Content,
		Action:        "created",
		PerformedBy:   &userID,
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to create translation history", err, nil)
		utils.InternalServerError(c, "Failed to create translation")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit translation creation", err, nil)
		utils.InternalServerError(c, "Failed to create translation")
		return
	}

	// Reload translation with relations
	if err := db.Preload("Key").Preload("Locale").Preload("Creator").Preload("Reviewer").
		Where("id = ?", translation.ID).First(&translation).Error; err != nil {
		utils.LogError(c, "Failed to reload created translation", err, nil)
		utils.InternalServerError(c, "Failed to retrieve created translation")
		return
	}

	// Convert to response format
	response := TranslationResponse{
		ID:         translation.ID.String(),
		KeyID:      translation.KeyID.String(),
		LocaleID:   translation.LocaleID.String(),
		Content:    translation.Content,
		IsFuzzy:    translation.IsFuzzy,
		IsReviewed: translation.IsReviewed,
		ReviewedBy: uuidPtrToStringPtr(translation.ReviewedBy),
		ReviewedAt: translation.ReviewedAt,
		CreatedBy:  uuidPtrToStringPtr(translation.CreatedBy),
		CreatedAt:  translation.CreatedAt,
		UpdatedAt:  translation.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Translation created successfully")
}

// ListTranslations handles GET /api/translations
func (h *TranslationHandler) ListTranslations(c *gin.Context) {
	// Get authenticated user from context
	_, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	// Parse query parameters
	keyID := c.Query("key_id")
	localeID := c.Query("locale_id")
	projectID := c.Query("project_id")

	// At least one filter must be provided
	if keyID == "" && localeID == "" && projectID == "" {
		utils.BadRequest(c, "At least one filter (key_id, locale_id, or project_id) must be provided")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Build query
	query := db.Model(&models.Translation{}).Preload("Key").Preload("Locale").Preload("Creator").Preload("Reviewer")

	if keyID != "" {
		if keyUUID, err := uuid.Parse(keyID); err == nil {
			query = query.Where("key_id = ?", keyUUID)
		} else {
			utils.BadRequest(c, "Invalid key_id format")
			return
		}
	}

	if localeID != "" {
		if localeUUID, err := uuid.Parse(localeID); err == nil {
			query = query.Where("locale_id = ?", localeUUID)
		} else {
			utils.BadRequest(c, "Invalid locale_id format")
			return
		}
	}

	if projectID != "" {
		if projectUUID, err := uuid.Parse(projectID); err == nil {
			query = query.Joins("JOIN translation_keys ON translations.key_id = translation_keys.id").
				Where("translation_keys.project_id = ?", projectUUID)
		} else {
			utils.BadRequest(c, "Invalid project_id format")
			return
		}
	}

	// Get translations
	var translations []models.Translation
	if err := query.Find(&translations).Error; err != nil {
		utils.LogError(c, "Failed to get translations", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translations")
		return
	}

	// Convert to response format
	var responses []TranslationResponse
	for _, translation := range translations {
		response := TranslationResponse{
			ID:         translation.ID.String(),
			KeyID:      translation.KeyID.String(),
			LocaleID:   translation.LocaleID.String(),
			Content:    translation.Content,
			IsFuzzy:    translation.IsFuzzy,
			IsReviewed: translation.IsReviewed,
			ReviewedBy: uuidPtrToStringPtr(translation.ReviewedBy),
			ReviewedAt: translation.ReviewedAt,
			CreatedBy:  uuidPtrToStringPtr(translation.CreatedBy),
			CreatedAt:  translation.CreatedAt,
			UpdatedAt:  translation.UpdatedAt,
		}
		responses = append(responses, response)
	}

	utils.Success(c, responses)
}

// GetTranslation handles GET /api/translations/:id
func (h *TranslationHandler) GetTranslation(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	translationIDStr := c.Param("id")
	translationID, err := uuid.Parse(translationIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get translation
	var translation models.Translation
	if err := db.Preload("Key.Project").Preload("Locale").Preload("Creator").Preload("Reviewer").
		Where("id = ?", translationID).First(&translation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation not found")
			return
		}
		utils.LogError(c, "Failed to get translation", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation")
		return
	}

	// Check organization access
	if translation.Key.Project != nil {
		if !h.checkOrganizationAccess(db, translation.Key.Project.OrganizationID, user.ID) {
			utils.Forbidden(c, "You don't have access to this translation")
			return
		}
	}

	// Convert to response format
	response := TranslationResponse{
		ID:         translation.ID.String(),
		KeyID:      translation.KeyID.String(),
		LocaleID:   translation.LocaleID.String(),
		Content:    translation.Content,
		IsFuzzy:    translation.IsFuzzy,
		IsReviewed: translation.IsReviewed,
		ReviewedBy: uuidPtrToStringPtr(translation.ReviewedBy),
		ReviewedAt: translation.ReviewedAt,
		CreatedBy:  uuidPtrToStringPtr(translation.CreatedBy),
		CreatedAt:  translation.CreatedAt,
		UpdatedAt:  translation.UpdatedAt,
	}

	utils.Success(c, response)
}

// UpdateTranslation handles PUT /api/translations/:id
func (h *TranslationHandler) UpdateTranslation(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	translationIDStr := c.Param("id")
	translationID, err := uuid.Parse(translationIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation ID format")
		return
	}

	var req UpdateTranslationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	userID, err := uuid.Parse(user.ID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get existing translation
	var translation models.Translation
	if err := db.Preload("Key.Project").Where("id = ?", translationID).First(&translation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation not found")
			return
		}
		utils.LogError(c, "Failed to get translation", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation")
		return
	}

	// Check organization access
	if translation.Key.Project != nil {
		if !h.checkOrganizationAccess(db, translation.Key.Project.OrganizationID, user.ID) {
			utils.Forbidden(c, "You don't have access to this translation")
			return
		}
	}

	// Store old content for history
	oldContent := translation.Content

	// Start transaction
	tx := db.Begin()

	// Update translation
	updates := map[string]interface{}{
		"content":    req.Content,
		"is_fuzzy":   req.IsFuzzy,
		"updated_at": utils.GetCurrentTime(),
	}

	if req.IsReviewed != nil {
		updates["is_reviewed"] = *req.IsReviewed
		if *req.IsReviewed {
			updates["reviewed_by"] = userID
			updates["reviewed_at"] = utils.GetCurrentTime()
		}
	}

	if err := tx.Model(&translation).Updates(updates).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to update translation", err, nil)
		utils.InternalServerError(c, "Failed to update translation")
		return
	}

	// Create history entry
	history := models.TranslationHistory{
		TranslationID: translation.ID,
		Content:       oldContent,
		Action:        "updated",
		PerformedBy:   &userID,
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to create translation history", err, nil)
		utils.InternalServerError(c, "Failed to update translation")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit translation update", err, nil)
		utils.InternalServerError(c, "Failed to update translation")
		return
	}

	// Reload translation with relations
	if err := db.Preload("Key").Preload("Locale").Preload("Creator").Preload("Reviewer").
		Where("id = ?", translation.ID).First(&translation).Error; err != nil {
		utils.LogError(c, "Failed to reload updated translation", err, nil)
		utils.InternalServerError(c, "Failed to retrieve updated translation")
		return
	}

	// Convert to response format
	response := TranslationResponse{
		ID:         translation.ID.String(),
		KeyID:      translation.KeyID.String(),
		LocaleID:   translation.LocaleID.String(),
		Content:    translation.Content,
		IsFuzzy:    translation.IsFuzzy,
		IsReviewed: translation.IsReviewed,
		ReviewedBy: uuidPtrToStringPtr(translation.ReviewedBy),
		ReviewedAt: translation.ReviewedAt,
		CreatedBy:  uuidPtrToStringPtr(translation.CreatedBy),
		CreatedAt:  translation.CreatedAt,
		UpdatedAt:  translation.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Translation updated successfully")
}

// GetTranslationHistory handles GET /api/translations/:id/history
func (h *TranslationHandler) GetTranslationHistory(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	translationIDStr := c.Param("id")
	translationID, err := uuid.Parse(translationIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if translation exists and user has access
	var translation models.Translation
	if err := db.Preload("Key.Project").Where("id = ?", translationID).First(&translation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation not found")
			return
		}
		utils.LogError(c, "Failed to get translation", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation")
		return
	}

	// Check organization access
	if translation.Key.Project != nil {
		if !h.checkOrganizationAccess(db, translation.Key.Project.OrganizationID, user.ID) {
			utils.Forbidden(c, "You don't have access to this translation")
			return
		}
	}

	// Get translation history
	var history []models.TranslationHistory
	if err := db.Preload("Performer").Where("translation_id = ?", translationID).
		Order("created_at DESC").Find(&history).Error; err != nil {
		utils.LogError(c, "Failed to get translation history", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation history")
		return
	}

	// Convert to response format
	var responses []TranslationHistoryResponse
	for _, h := range history {
		response := TranslationHistoryResponse{
			ID:            h.ID.String(),
			TranslationID: h.TranslationID.String(),
			Content:       h.Content,
			Action:        h.Action,
			PerformedBy:   uuidPtrToStringPtr(h.PerformedBy),
			CreatedAt:     h.CreatedAt,
		}
		responses = append(responses, response)
	}

	utils.Success(c, responses)
}

// DeleteTranslationKey handles DELETE /api/translations/keys/:id
func (h *TranslationHandler) DeleteTranslationKey(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := uuid.Parse(keyIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid translation key ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get existing translation key with relations
	var key models.TranslationKey
	if err := db.Preload("Resource.Project").Preload("Project").
		Where("id = ?", keyID).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Translation key not found")
			return
		}
		utils.LogError(c, "Failed to get translation key", err, nil)
		utils.InternalServerError(c, "Failed to retrieve translation key")
		return
	}

	// Check organization access
	var organizationID uuid.UUID
	if key.ResourceID != nil && key.Resource != nil {
		organizationID = key.Resource.Project.OrganizationID
	} else if key.ProjectID != nil && key.Project != nil {
		organizationID = key.Project.OrganizationID
	} else {
		utils.InternalServerError(c, "Translation key has no valid resource or project")
		return
	}

	if !h.checkOrganizationAccess(db, organizationID, user.ID) {
		utils.Forbidden(c, "You don't have access to this translation key")
		return
	}

	// Start transaction for deletion
	tx := db.Begin()

	// First, get all translation IDs associated with this key
	var translationIDs []uuid.UUID
	if err := tx.Model(&models.Translation{}).Where("key_id = ?", keyID).Pluck("id", &translationIDs).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to get translation IDs", err, nil)
		utils.InternalServerError(c, "Failed to delete translation key")
		return
	}

	// Delete all associated translation history first (if there are translations)
	if len(translationIDs) > 0 {
		if err := tx.Where("translation_id IN ?", translationIDs).Delete(&models.TranslationHistory{}).Error; err != nil {
			tx.Rollback()
			utils.LogError(c, "Failed to delete associated translation history", err, nil)
			utils.InternalServerError(c, "Failed to delete translation key")
			return
		}
	}

	// Then delete all associated translations
	if err := tx.Where("key_id = ?", keyID).Delete(&models.Translation{}).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to delete associated translations", err, nil)
		utils.InternalServerError(c, "Failed to delete translation key")
		return
	}

	// Delete the translation key
	if err := tx.Delete(&key).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to delete translation key", err, nil)
		utils.InternalServerError(c, "Failed to delete translation key")
		return
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit translation key deletion", err, nil)
		utils.InternalServerError(c, "Failed to delete translation key")
		return
	}

	utils.SuccessWithMessage(c, nil, "Translation key deleted successfully")
}

// Helper function to check organization access
func (h *TranslationHandler) checkOrganizationAccess(db *gorm.DB, organizationID uuid.UUID, userID string) bool {
	var member models.OrganizationMembership
	err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?",
		organizationID, userID, true).First(&member).Error
	return err == nil
}
