package tests

import (
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/middleware"
	"adc-multi-languages/utils"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
)

// TestConfigLoading tests configuration loading
func TestConfigLoading(t *testing.T) {
	// Set test environment variables
	t.Setenv("APP_ENV", "test")
	t.Setenv("JWT_SECRET", "test-secret")
	t.Setenv("DATABASE_URL", "postgres://test:test@localhost:5432/test")

	cfg, err := config.Load()
	assert.NoError(t, err)
	assert.NotNil(t, cfg)
	assert.Equal(t, "test", cfg.Env)
	assert.Equal(t, "test-secret", cfg.JWTSecret)
}

// TestJWTTokenGeneration tests JWT token generation and validation
func TestJWTTokenGeneration(t *testing.T) {
	authConfig := middleware.AuthConfig{
		JWTSecret:          "test-secret-key",
		JWTExpirationHours: 24,
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"
	email := "<EMAIL>"
	orgID := "123e4567-e89b-12d3-a456-426614174001"

	// Test token generation
	token, err := middleware.GenerateJWT(authConfig, userID, email, orgID)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	// Test token validation
	claims, err := middleware.ValidateToken(authConfig, token)
	assert.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, email, claims.Email)
	assert.Equal(t, orgID, claims.OrganizationID)
}

// TestPasswordHashing tests password hashing functionality
func TestPasswordHashing(t *testing.T) {
	password := "testpassword123"

	// Test password hashing
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	assert.NoError(t, err)
	assert.NotEmpty(t, hashedPassword)

	// Test password verification
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	assert.NoError(t, err)

	// Test wrong password
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte("wrongpassword"))
	assert.Error(t, err)
}

// TestPaginationUtils tests pagination utility functions
func TestPaginationUtils(t *testing.T) {
	// Test valid pagination parameters
	params := utils.PaginationParams{
		Page:    1,
		PerPage: 10,
		Offset:  0,
	}

	limit, offset := params.GetLimitOffset()
	assert.Equal(t, uint32(10), limit)
	assert.Equal(t, uint32(0), offset)

	// Test pagination with offset
	params = utils.PaginationParams{
		Page:    3,
		PerPage: 20,
		Offset:  40,
	}

	limit, offset = params.GetLimitOffset()
	assert.Equal(t, uint32(20), limit)
	assert.Equal(t, uint32(40), offset)

	// Test has next page
	hasNext := params.HasNextPage(100) // Total 100 items
	assert.True(t, hasNext)

	hasNext = params.HasNextPage(50) // Total 50 items
	assert.False(t, hasNext)

	// Test has previous page
	hasPrev := params.HasPreviousPage()
	assert.True(t, hasPrev)

	params.Page = 1
	hasPrev = params.HasPreviousPage()
	assert.False(t, hasPrev)
}

// TestResponseUtils tests response utility functions
func TestResponseUtils(t *testing.T) {
	// Test current timestamp
	timestamp := utils.GetCurrentTimestamp()
	assert.NotZero(t, timestamp)

	// Test timestamp formatting
	formatted := utils.FormatTimestamp(timestamp)
	assert.NotEmpty(t, formatted)
	assert.Contains(t, formatted, "T") // ISO 8601 format should contain T
}

// TestConfigValidation tests configuration validation
func TestConfigValidation(t *testing.T) {
	// Test production environment requires proper JWT secret
	t.Setenv("APP_ENV", "production")
	t.Setenv("JWT_SECRET", "your-secret-key") // Default value

	cfg, err := config.Load()
	assert.Error(t, err) // Should fail in production with default secret

	// Test with proper secret
	t.Setenv("JWT_SECRET", "proper-production-secret")
	cfg, err = config.Load()
	assert.NoError(t, err)
	assert.True(t, cfg.IsProduction())
	assert.False(t, cfg.IsDevelopment())
}

// TestEnvironmentDetection tests environment detection
func TestEnvironmentDetection(t *testing.T) {
	// Test development environment
	t.Setenv("APP_ENV", "development")
	t.Setenv("JWT_SECRET", "dev-secret")

	cfg, err := config.Load()
	assert.NoError(t, err)
	assert.True(t, cfg.IsDevelopment())
	assert.False(t, cfg.IsProduction())

	// Test test environment
	t.Setenv("APP_ENV", "test")
	cfg, err = config.Load()
	assert.NoError(t, err)
	assert.False(t, cfg.IsDevelopment())
	assert.False(t, cfg.IsProduction())
}

// TestDatabaseURLConstruction tests database URL construction
func TestDatabaseURLConstruction(t *testing.T) {
	// Test with individual components
	t.Setenv("DB_HOST", "localhost")
	t.Setenv("DB_PORT", "5432")
	t.Setenv("DB_USER", "testuser")
	t.Setenv("DB_PASSWORD", "testpass")
	t.Setenv("DB_NAME", "testdb")
	t.Setenv("DB_SSL_MODE", "disable")
	t.Setenv("DATABASE_URL", "") // Clear DATABASE_URL

	cfg, err := config.Load()
	assert.NoError(t, err)

	expectedURL := "postgres://testuser:testpass@localhost:5432/testdb?sslmode=disable"
	assert.Equal(t, expectedURL, cfg.GetDatabaseURL())

	// Test with direct DATABASE_URL
	directURL := "********************************/db"
	t.Setenv("DATABASE_URL", directURL)

	cfg, err = config.Load()
	assert.NoError(t, err)
	assert.Equal(t, directURL, cfg.GetDatabaseURL())
}

// TestJWTRefreshToken tests refresh token generation
func TestJWTRefreshToken(t *testing.T) {
	authConfig := middleware.AuthConfig{
		JWTSecret:                "test-secret-key",
		JWTRefreshExpirationDays: 30,
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"

	// Test refresh token generation
	refreshToken, err := middleware.GenerateRefreshToken(authConfig, userID)
	assert.NoError(t, err)
	assert.NotEmpty(t, refreshToken)

	// Test refresh token validation
	claims, err := middleware.ValidateToken(authConfig, refreshToken)
	assert.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Empty(t, claims.Email) // Refresh tokens don't include email
	assert.Empty(t, claims.OrganizationID)
}

// TestPasswordResetToken tests password reset token generation
func TestPasswordResetToken(t *testing.T) {
	authConfig := middleware.AuthConfig{
		JWTSecret: "test-secret-key",
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"
	email := "<EMAIL>"

	// Test password reset token generation
	resetToken, err := middleware.GeneratePasswordResetToken(authConfig, userID, email)
	assert.NoError(t, err)
	assert.NotEmpty(t, resetToken)

	// Test token validation
	claims, err := middleware.ValidateToken(authConfig, resetToken)
	assert.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, email, claims.Email)
}

// TestEmailVerificationToken tests email verification token generation
func TestEmailVerificationToken(t *testing.T) {
	authConfig := middleware.AuthConfig{
		JWTSecret: "test-secret-key",
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"
	email := "<EMAIL>"

	// Test email verification token generation
	verificationToken, err := middleware.GenerateEmailVerificationToken(authConfig, userID, email)
	assert.NoError(t, err)
	assert.NotEmpty(t, verificationToken)

	// Test token validation
	claims, err := middleware.ValidateToken(authConfig, verificationToken)
	assert.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, email, claims.Email)
}

// TestInvalidJWTToken tests invalid JWT token scenarios
func TestInvalidJWTToken(t *testing.T) {
	authConfig := middleware.AuthConfig{
		JWTSecret: "test-secret-key",
	}

	// Test invalid token format
	_, err := middleware.ValidateToken(authConfig, "invalid.token.format")
	assert.Error(t, err)

	// Test token with wrong secret
	wrongAuthConfig := middleware.AuthConfig{
		JWTSecret: "wrong-secret-key",
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"
	email := "<EMAIL>"

	token, err := middleware.GenerateJWT(authConfig, userID, email, "")
	assert.NoError(t, err)

	// Try to validate with wrong secret
	_, err = middleware.ValidateToken(wrongAuthConfig, token)
	assert.Error(t, err)

	// Test empty token
	_, err = middleware.ValidateToken(authConfig, "")
	assert.Error(t, err)
}
