package tests

import (
	"fmt"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TestConfig holds test-specific configuration
type TestConfig struct {
	DatabaseURL string
	JWTSecret   string
	TestDBName  string
}

// SetupTestEnvironment sets up the test environment
func SetupTestEnvironment() (*TestConfig, error) {
	// Set test environment variables
	testEnvVars := map[string]string{
		"APP_ENV":              "test",
		"DATABASE_URL":         "postgresql://postgres:<EMAIL>:5432/postgres",
		"JWT_SECRET":           "test-secret-key-for-testing-only",
		"JWT_REFRESH_SECRET":   "test-refresh-secret-key",
		"JWT_EXPIRATION_HOURS": "24",
		"LOG_LEVEL":            "error", // Reduce log noise during tests
		"PORT":                 "8301",  // Different port for tests
	}

	for key, value := range testEnvVars {
		os.Setenv(key, value)
	}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	return &TestConfig{
		DatabaseURL: testEnvVars["DATABASE_URL"],
		JWTSecret:   testEnvVars["JWT_SECRET"],
		TestDBName:  "adc_test",
	}, nil
}

// SetupTestDatabase sets up a clean test database
func SetupTestDatabase(cfg *config.Config) (*gorm.DB, error) {
	// Connect to database
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}

	err := database.Connect(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to test database: %w", err)
	}

	db := database.GetDB()

	// Clean database
	if err := CleanTestDatabase(db); err != nil {
		return nil, fmt.Errorf("failed to clean test database: %w", err)
	}

	// Run migrations
	if err := MigrateTestDatabase(db); err != nil {
		return nil, fmt.Errorf("failed to migrate test database: %w", err)
	}

	return db, nil
}

// CleanTestDatabase removes all test data
func CleanTestDatabase(db *gorm.DB) error {
	// Drop all tables in reverse dependency order
	tables := []interface{}{
		&models.TranslationHistory{},
		&models.Translation{},
		&models.TranslationKey{},
		&models.Resource{},
		&models.ProjectLocale{},
		&models.AICreditUsage{},
		&models.AICreditTransaction{},
		&models.APICallLog{},
		&models.PermissionAuditLog{},
		&models.APIKey{},
		&models.OrganizationSubscription{},
		&models.CreditLimit{},
		&models.AuditLog{},
		&models.Project{},
		&models.OrganizationMembership{},
		&models.Organization{},
		&models.PasswordResetToken{},
		&models.EmailVerificationToken{},
		&models.User{},
		&models.Locale{},
		&models.SubscriptionPlan{},
		&models.PermissionGroup{},
	}

	for _, table := range tables {
		if err := db.Unscoped().Where("1 = 1").Delete(table).Error; err != nil {
			return fmt.Errorf("failed to clean table %T: %w", table, err)
		}
	}

	return nil
}

// MigrateTestDatabase runs database migrations for tests
func MigrateTestDatabase(db *gorm.DB) error {
	models := []interface{}{
		&models.User{},
		&models.Organization{},
		&models.OrganizationMembership{},
		&models.Project{},
		&models.ProjectLocale{},
		&models.Resource{},
		&models.Locale{},
		&models.TranslationKey{},
		&models.Translation{},
		&models.TranslationHistory{},
		&models.APIKey{},
		&models.APICallLog{},
		&models.PermissionAuditLog{},
		&models.PermissionGroup{},
		&models.AICreditTransaction{},
		&models.AICreditUsage{},
		&models.SubscriptionPlan{},
		&models.OrganizationSubscription{},
		&models.PasswordResetToken{},
		&models.EmailVerificationToken{},
		&models.CreditLimit{},
		&models.AuditLog{},
	}

	return database.Migrate(models...)
}

// TeardownTestEnvironment cleans up after tests
func TeardownTestEnvironment(db *gorm.DB) error {
	if db != nil {
		if err := CleanTestDatabase(db); err != nil {
			return fmt.Errorf("failed to clean test database: %w", err)
		}

		if err := database.Close(); err != nil {
			return fmt.Errorf("failed to close database connection: %w", err)
		}
	}

	return nil
}

// SkipIfShort skips the test if running in short mode
func SkipIfShort(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}
}

// RequireDatabase checks if database is available for testing
func RequireDatabase(t *testing.T, db *gorm.DB) {
	if db == nil {
		t.Fatal("Database connection is required for this test")
	}

	// Test database connection
	sqlDB, err := db.DB()
	if err != nil {
		t.Fatalf("Failed to get database instance: %v", err)
	}

	if err := sqlDB.Ping(); err != nil {
		t.Fatalf("Database ping failed: %v", err)
	}
}

// CreateTestUser creates a test user for testing
func CreateTestUser(db *gorm.DB, email, password string) (*models.User, error) {
	user := &models.User{
		Email:         email,
		PasswordHash:  password, // Should be hashed in real usage
		EmailVerified: true,
		IsActive:      true,
	}

	if err := db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create test user: %w", err)
	}

	return user, nil
}

// CreateTestOrganization creates a test organization
func CreateTestOrganization(db *gorm.DB, name, slug string, ownerID string) (*models.Organization, error) {
	org := &models.Organization{
		Name:               name,
		Slug:               slug,
		SubscriptionTier:   "free",
		AICreditsRemaining: &[]int{1000}[0],
	}

	if err := db.Create(org).Error; err != nil {
		return nil, fmt.Errorf("failed to create test organization: %w", err)
	}

	return org, nil
}

// CreateTestProject creates a test project
func CreateTestProject(db *gorm.DB, name, slug string, orgID string) (*models.Project, error) {
	project := &models.Project{
		Name:          name,
		Slug:          slug,
		DefaultLocale: &[]string{"en"}[0],
	}

	if err := db.Create(project).Error; err != nil {
		return nil, fmt.Errorf("failed to create test project: %w", err)
	}

	return project, nil
}

// CreateTestLocales creates standard test locales
func CreateTestLocales(db *gorm.DB) error {
	locales := []models.Locale{
		{Code: "en", Name: "English", NativeName: "English", Direction: "ltr", IsActive: true},
		{Code: "es", Name: "Spanish", NativeName: "Español", Direction: "ltr", IsActive: true},
		{Code: "fr", Name: "French", NativeName: "Français", Direction: "ltr", IsActive: true},
		{Code: "de", Name: "German", NativeName: "Deutsch", Direction: "ltr", IsActive: true},
		{Code: "ja", Name: "Japanese", NativeName: "日本語", Direction: "ltr", IsActive: true},
		{Code: "ar", Name: "Arabic", NativeName: "العربية", Direction: "rtl", IsActive: true},
	}

	for _, locale := range locales {
		if err := db.Create(&locale).Error; err != nil {
			return fmt.Errorf("failed to create test locale %s: %w", locale.Code, err)
		}
	}

	return nil
}

// CreateTestSubscriptionPlan creates a test subscription plan
func CreateTestSubscriptionPlan(db *gorm.DB) (*models.SubscriptionPlan, error) {
	plan := &models.SubscriptionPlan{
		Name:                      "Test Plan",
		Description:               "Test subscription plan",
		PriceMonthly:              9.99,
		AICreditsMonthlyAllowance: 5000,
		MaxProjects:               &[]int{10}[0],
		MaxUsers:                  &[]int{5}[0],
		Features:                  `["basic_features", "api_access"]`,
		IsActive:                  true,
	}

	if err := db.Create(plan).Error; err != nil {
		return nil, fmt.Errorf("failed to create test subscription plan: %w", err)
	}

	return plan, nil
}
