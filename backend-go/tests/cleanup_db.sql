-- Cleanup script for integration tests
-- Run this to clean up test data from Supabase database

-- Clean test data (be very careful with this!)
DELETE FROM api_keys WHERE name LIKE '%Test%' OR name LIKE '%test%';
DELETE FROM users WHERE email LIKE '%@test.example.com' OR email LIKE '%test%';
DELETE FROM organizations WHERE name LIKE '%Test%' OR slug LIKE '%test%';
DELETE FROM projects WHERE name LIKE '%Test%' OR slug LIKE '%test%';

-- Add the missing column to api_keys if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'key') THEN
        ALTER TABLE api_keys ADD COLUMN key text;
    END IF;
END $$;

-- Make the key column unique if it's not already
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE table_name = 'api_keys' AND constraint_name = 'api_keys_key_key') THEN
        ALTER TABLE api_keys ADD CONSTRAINT api_keys_key_key UNIQUE (key);
    END IF;
END $$;

-- Clean up any orphaned records
DELETE FROM organization_memberships WHERE user_id NOT IN (SELECT id FROM users);
DELETE FROM projects WHERE organization_id NOT IN (SELECT id FROM organizations);
DELETE FROM translation_keys WHERE project_id NOT IN (SELECT id FROM projects);
DELETE FROM translations WHERE key_id NOT IN (SELECT id FROM translation_keys);
DELETE FROM translation_histories WHERE translation_id NOT IN (SELECT id FROM translations);

COMMIT;
