# Integration Tests Makefile for Go Backend

.PHONY: help test test-verbose test-short test-coverage test-specific clean setup-db teardown-db

# Default target
help:
	@echo "Integration Tests for Go Backend"
	@echo ""
	@echo "Available targets:"
	@echo "  test           - Run all integration tests"
	@echo "  test-verbose   - Run tests with verbose output"
	@echo "  test-short     - Run tests in short mode (skip slow tests)"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  test-specific  - Run specific test pattern (use PATTERN=TestName)"
	@echo "  setup-db       - Setup test database"
	@echo "  teardown-db    - Teardown test database"
	@echo "  clean          - Clean test artifacts"
	@echo ""
	@echo "Environment variables:"
	@echo "  DATABASE_URL   - Test database URL (default: postgres://postgres:password@localhost:5432/adc_test?sslmode=disable)"
	@echo "  PATTERN        - Test pattern for test-specific target"
	@echo ""
	@echo "Examples:"
	@echo "  make test"
	@echo "  make test-verbose"
	@echo "  make test-coverage"
	@echo "  make test-specific PATTERN=TestAuth"

# Set default database URL if not provided
DATABASE_URL ?= postgres://postgres:password@localhost:5432/adc_test?sslmode=disable

# Export environment variables for tests
export APP_ENV=test
export DATABASE_URL
export JWT_SECRET=test-secret-key-for-testing-only
export JWT_REFRESH_SECRET=test-refresh-secret-key
export JWT_EXPIRATION_HOURS=24
export LOG_LEVEL=error
export PORT=8301

# Run all integration tests
test:
	@echo "🚀 Running integration tests..."
	@go test -timeout 10m ./...

# Run tests with verbose output
test-verbose:
	@echo "🚀 Running integration tests (verbose)..."
	@go test -v -timeout 10m ./...

# Run tests in short mode
test-short:
	@echo "🚀 Running integration tests (short mode)..."
	@go test -short -timeout 5m ./...

# Run tests with coverage
test-coverage:
	@echo "🚀 Running integration tests with coverage..."
	@go test -coverprofile=coverage.out -covermode=atomic -timeout 10m ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "📊 Coverage report generated: coverage.html"
	@go tool cover -func=coverage.out | tail -1

# Run specific test pattern
test-specific:
	@if [ -z "$(PATTERN)" ]; then \
		echo "❌ Error: PATTERN is required. Use: make test-specific PATTERN=TestName"; \
		exit 1; \
	fi
	@echo "🚀 Running tests matching pattern: $(PATTERN)"
	@go test -v -run "$(PATTERN)" -timeout 10m ./...

# Run tests with race detection
test-race:
	@echo "🚀 Running integration tests with race detection..."
	@go test -race -timeout 15m ./...

# Run tests and generate JSON output
test-json:
	@echo "🚀 Running integration tests (JSON output)..."
	@go test -json -timeout 10m ./... > test-results.json
	@echo "📄 Test results saved to: test-results.json"

# Setup test database
setup-db:
	@echo "🔧 Setting up test database..."
	@psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS adc_test;" || true
	@psql -h localhost -U postgres -c "CREATE DATABASE adc_test;"
	@echo "✅ Test database created"

# Teardown test database
teardown-db:
	@echo "🧹 Tearing down test database..."
	@psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS adc_test;" || true
	@echo "✅ Test database removed"

# Clean test artifacts
clean:
	@echo "🧹 Cleaning test artifacts..."
	@rm -f coverage.out coverage.html test-results.json
	@rm -f ../adc-backend-test
	@echo "✅ Test artifacts cleaned"

# Build application for testing
build:
	@echo "🔨 Building application for testing..."
	@cd .. && go build -o adc-backend-test .
	@echo "✅ Application built"

# Run tests with database setup and teardown
test-full: setup-db test teardown-db

# Run tests with all checks (coverage, race detection)
test-all: clean setup-db test-coverage test-race teardown-db
	@echo "🎉 All tests completed successfully!"

# Check test dependencies
check-deps:
	@echo "📋 Checking test dependencies..."
	@command -v go >/dev/null 2>&1 || { echo "❌ Go is not installed"; exit 1; }
	@command -v psql >/dev/null 2>&1 || { echo "❌ PostgreSQL client is not installed"; exit 1; }
	@echo "✅ All dependencies are available"

# Run linting on test files
lint:
	@echo "🔍 Linting test files..."
	@go vet ./...
	@go fmt ./...
	@echo "✅ Linting completed"

# Run tests in CI environment
test-ci: check-deps setup-db test-coverage test-race teardown-db
	@echo "🤖 CI tests completed"

# Watch tests (requires entr or similar tool)
test-watch:
	@echo "👀 Watching for changes and running tests..."
	@find . -name "*.go" | entr -c make test

# Benchmark tests
benchmark:
	@echo "⚡ Running benchmark tests..."
	@go test -bench=. -benchmem -timeout 15m ./...

# Profile tests
profile:
	@echo "📊 Profiling tests..."
	@go test -cpuprofile=cpu.prof -memprofile=mem.prof -timeout 15m ./...
	@echo "📄 CPU profile: cpu.prof"
	@echo "📄 Memory profile: mem.prof"

# Generate test documentation
docs:
	@echo "📚 Generating test documentation..."
	@go doc -all ./... > test-docs.txt
	@echo "📄 Test documentation: test-docs.txt"

# Run security checks on tests
security:
	@echo "🔒 Running security checks..."
	@go list -json -m all | nancy sleuth || echo "⚠️  Nancy not installed, skipping security check"

# Install test tools
install-tools:
	@echo "🛠️  Installing test tools..."
	@go install github.com/sonatypecommunity/nancy@latest || echo "⚠️  Failed to install nancy"
	@echo "✅ Test tools installed"
